
import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface Book {
  id: string;
  title: string;
  author: string;
  category: string;
  description: string;
  price: number;
  duration: string;
  image: string;
  available: boolean;
}

export interface Order {
  id: string;
  bookId: string;
  bookTitle: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  startDate: string;
  endDate: string;
  totalPrice: number;
  status: 'requested' | 'approved' | 'rejected';
  createdAt: string;
}

interface AppContextType {
  isAdminLoggedIn: boolean;
  books: Book[];
  orders: Order[];
  login: (username: string, password: string) => boolean;
  logout: () => void;
  addBook: (book: Omit<Book, 'id'>) => void;
  updateBook: (id: string, book: Partial<Book>) => void;
  deleteBook: (id: string) => void;
  addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'status'>) => void;
  updateOrderStatus: (id: string, status: 'approved' | 'rejected') => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isAdminLoggedIn, setIsAdminLoggedIn] = useState(false);
  const [books, setBooks] = useState<Book[]>([
    {
      id: '1',
      title: 'Clean Code',
      author: 'Robert C. Martin',
      category: 'Software Engineering',
      description: 'A handbook of agile software craftsmanship',
      price: 25000,
      duration: 'per week',
      image: '/placeholder.svg',
      available: true,
    },
    {
      id: '2',
      title: 'JavaScript: The Good Parts',
      author: 'Douglas Crockford',
      category: 'JavaScript',
      description: 'Unearthing the excellence in JavaScript',
      price: 20000,
      duration: 'per week',
      image: '/placeholder.svg',
      available: true,
    },
    {
      id: '3',
      title: 'Python Crash Course',
      author: 'Eric Matthes',
      category: 'Python',
      description: 'A hands-on, project-based introduction to programming',
      price: 30000,
      duration: 'per week',
      image: '/placeholder.svg',
      available: true,
    },
  ]);
  const [orders, setOrders] = useState<Order[]>([]);

  const login = (username: string, password: string): boolean => {
    if (username === 'admin' && password === 'admin123') {
      setIsAdminLoggedIn(true);
      return true;
    }
    return false;
  };

  const logout = () => {
    setIsAdminLoggedIn(false);
  };

  const addBook = (book: Omit<Book, 'id'>) => {
    const newBook: Book = {
      ...book,
      id: Date.now().toString(),
    };
    setBooks(prev => [...prev, newBook]);
  };

  const updateBook = (id: string, updatedBook: Partial<Book>) => {
    setBooks(prev => prev.map(book => 
      book.id === id ? { ...book, ...updatedBook } : book
    ));
  };

  const deleteBook = (id: string) => {
    setBooks(prev => prev.filter(book => book.id !== id));
  };

  const addOrder = (order: Omit<Order, 'id' | 'createdAt' | 'status'>) => {
    const newOrder: Order = {
      ...order,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      status: 'requested',
    };
    setOrders(prev => [...prev, newOrder]);
  };

  const updateOrderStatus = (id: string, status: 'approved' | 'rejected') => {
    setOrders(prev => prev.map(order => 
      order.id === id ? { ...order, status } : order
    ));
  };

  return (
    <AppContext.Provider value={{
      isAdminLoggedIn,
      books,
      orders,
      login,
      logout,
      addBook,
      updateBook,
      deleteBook,
      addOrder,
      updateOrderStatus,
    }}>
      {children}
    </AppContext.Provider>
  );
};
