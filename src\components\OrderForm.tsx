
import React, { useState } from 'react';
import { useApp, Book } from '../contexts/AppContext';
import { Calendar, User, Mail, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';

interface OrderFormProps {
  book: Book;
  onClose: () => void;
}

const OrderForm: React.FC<OrderFormProps> = ({ book, onClose }) => {
  const { addOrder } = useApp();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    startDate: '',
    endDate: '',
  });

  const calculateTotalPrice = () => {
    if (!formData.startDate || !formData.endDate) return 0;
    
    const start = new Date(formData.startDate);
    const end = new Date(formData.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    
    let days = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (days === 0) days = 1;
    
    // Calculate based on duration type
    let multiplier = days;
    if (book.duration === 'per week') {
      multiplier = Math.ceil(days / 7);
    } else if (book.duration === 'per month') {
      multiplier = Math.ceil(days / 30);
    }
    
    return book.price * multiplier;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const totalPrice = calculateTotalPrice();
    
    addOrder({
      bookId: book.id,
      bookTitle: book.title,
      customerName: formData.customerName,
      customerEmail: formData.customerEmail,
      customerPhone: formData.customerPhone,
      startDate: formData.startDate,
      endDate: formData.endDate,
      totalPrice,
    });
    
    toast({
      title: "Pesanan berhasil dibuat!",
      description: "Pesanan Anda akan segera diproses oleh admin.",
    });
    
    onClose();
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Set minimum date to today
  const today = new Date().toISOString().split('T')[0];

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Sewa Buku: {book.title}</DialogTitle>
        </DialogHeader>
        
        <div className="mb-4 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900">Detail Buku</h3>
          <p className="text-blue-700">{book.title} - {book.author}</p>
          <p className="text-blue-600">Rp {book.price.toLocaleString('id-ID')} {book.duration}</p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="customerName">
              <User className="w-4 h-4 inline mr-2" />
              Nama Lengkap
            </Label>
            <Input
              id="customerName"
              value={formData.customerName}
              onChange={(e) => handleChange('customerName', e.target.value)}
              placeholder="Masukkan nama lengkap"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="customerEmail">
              <Mail className="w-4 h-4 inline mr-2" />
              Email
            </Label>
            <Input
              id="customerEmail"
              type="email"
              value={formData.customerEmail}
              onChange={(e) => handleChange('customerEmail', e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="customerPhone">
              <Phone className="w-4 h-4 inline mr-2" />
              Nomor Telepon
            </Label>
            <Input
              id="customerPhone"
              type="tel"
              value={formData.customerPhone}
              onChange={(e) => handleChange('customerPhone', e.target.value)}
              placeholder="08xxxxxxxxxx"
              required
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">
                <Calendar className="w-4 h-4 inline mr-2" />
                Tanggal Mulai
              </Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => handleChange('startDate', e.target.value)}
                min={today}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="endDate">
                <Calendar className="w-4 h-4 inline mr-2" />
                Tanggal Selesai
              </Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => handleChange('endDate', e.target.value)}
                min={formData.startDate || today}
                required
              />
            </div>
          </div>
          
          {formData.startDate && formData.endDate && (
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-900">Total Pembayaran</h4>
              <p className="text-2xl font-bold text-green-600">
                Rp {calculateTotalPrice().toLocaleString('id-ID')}
              </p>
            </div>
          )}
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
              Buat Pesanan
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default OrderForm;
