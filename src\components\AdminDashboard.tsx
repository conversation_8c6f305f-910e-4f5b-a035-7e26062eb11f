
import React, { useState } from 'react';
import { useApp } from '../contexts/AppContext';
import { LogOut, Plus, Edit, Trash2, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import BookForm from './BookForm';
import { useToast } from '@/hooks/use-toast';

const AdminDashboard = () => {
  const { logout, books, orders, deleteBook, updateOrderStatus } = useApp();
  const [editingBook, setEditingBook] = useState(null);
  const [showBookForm, setShowBookForm] = useState(false);
  const { toast } = useToast();

  const handleDeleteBook = (id: string) => {
    deleteBook(id);
    toast({
      title: "Buku dihapus",
      description: "Buku berhasil dihapus dari katalog.",
    });
  };

  const handleApproveOrder = (id: string) => {
    updateOrderStatus(id, 'approved');
    toast({
      title: "Pesanan disetujui",
      description: "Pesanan berhasil disetujui.",
    });
  };

  const handleRejectOrder = (id: string) => {
    updateOrderStatus(id, 'rejected');
    toast({
      title: "Pesanan ditolak",
      description: "Pesanan telah ditolak.",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'requested': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'requested': return 'Menunggu';
      case 'approved': return 'Disetujui';
      case 'rejected': return 'Ditolak';
      default: return status;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <Button onClick={logout} variant="outline">
              <LogOut className="w-4 h-4 mr-2" />
              Keluar
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="catalog" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="catalog">Kelola Katalog</TabsTrigger>
            <TabsTrigger value="orders">Kelola Pesanan</TabsTrigger>
          </TabsList>

          <TabsContent value="catalog" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">Katalog Buku</h2>
              <Button onClick={() => setShowBookForm(true)} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Tambah Buku
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {books.map((book) => (
                <Card key={book.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{book.title}</CardTitle>
                        <CardDescription>{book.author}</CardDescription>
                      </div>
                      <div className="flex space-x-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setEditingBook(book);
                            setShowBookForm(true);
                          }}
                        >
                          <Edit className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteBook(book.id)}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-2">{book.description}</p>
                    <div className="flex justify-between items-center">
                      <Badge variant="secondary">{book.category}</Badge>
                      <span className="font-semibold text-green-600">
                        Rp {book.price.toLocaleString('id-ID')} / {book.duration}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="orders" className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Pesanan</h2>
            
            <div className="space-y-4">
              {orders.map((order) => (
                <Card key={order.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">{order.bookTitle}</h3>
                        <p className="text-gray-600">{order.customerName} - {order.customerEmail}</p>
                        <p className="text-sm text-gray-500">{order.customerPhone}</p>
                      </div>
                      <Badge className={getStatusColor(order.status)}>
                        {getStatusText(order.status)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Mulai:</span>
                        <p>{new Date(order.startDate).toLocaleDateString('id-ID')}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Selesai:</span>
                        <p>{new Date(order.endDate).toLocaleDateString('id-ID')}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Total:</span>
                        <p className="font-semibold text-green-600">Rp {order.totalPrice.toLocaleString('id-ID')}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Tanggal Pesan:</span>
                        <p>{new Date(order.createdAt).toLocaleDateString('id-ID')}</p>
                      </div>
                    </div>

                    {order.status === 'requested' && (
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          onClick={() => handleApproveOrder(order.id)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Check className="w-4 h-4 mr-1" />
                          Setujui
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleRejectOrder(order.id)}
                        >
                          Tolak
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
              
              {orders.length === 0 && (
                <Card>
                  <CardContent className="p-8 text-center">
                    <p className="text-gray-500">Belum ada pesanan masuk.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </main>

      {showBookForm && (
        <BookForm
          book={editingBook}
          onClose={() => {
            setShowBookForm(false);
            setEditingBook(null);
          }}
        />
      )}
    </div>
  );
};

export default AdminDashboard;
