
import React from 'react';
import { useApp } from '../contexts/AppContext';
import AdminLogin from '../components/AdminLogin';
import AdminDashboard from '../components/AdminDashboard';
import BookCatalog from '../components/BookCatalog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BookOpen, Users, ShoppingCart, CheckCircle } from 'lucide-react';

const Index = () => {
  const { isAdminLoggedIn } = useApp();
  const [showAdminLogin, setShowAdminLogin] = React.useState(false);
  const [showCatalog, setShowCatalog] = React.useState(false);

  if (isAdminLoggedIn) {
    return <AdminDashboard />;
  }

  if (showAdminLogin) {
    return <AdminLogin />;
  }

  if (showCatalog) {
    return <BookCatalog />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Hero Section */}
      <header className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-600 rounded-full mb-6">
                <BookOpen className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                Rental Buku
                <span className="text-blue-600"> Programming</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Platform terpercaya untuk menyewa buku-buku programming terbaik. 
                Tingkatkan skill coding Anda dengan koleksi buku berkualitas tinggi.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                onClick={() => setShowCatalog(true)}
                className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-3 h-auto"
              >
                <ShoppingCart className="w-5 h-5 mr-2" />
                Lihat Katalog Buku
              </Button>
              <Button 
                onClick={() => setShowAdminLogin(true)}
                variant="outline"
                className="text-lg px-8 py-3 h-auto"
              >
                <Users className="w-5 h-5 mr-2" />
                Login Admin
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Mengapa Memilih Kami?
            </h2>
            <p className="text-xl text-gray-600">
              Solusi terbaik untuk kebutuhan belajar programming Anda
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="w-8 h-8 text-blue-600" />
                </div>
                <CardTitle>Koleksi Lengkap</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Berbagai buku programming dari penulis terpercaya dan materi terkini
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <CardTitle>Mudah & Cepat</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Proses pemesanan yang simple dan persetujuan yang cepat
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-purple-600" />
                </div>
                <CardTitle>Layanan Prima</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Admin yang responsif dan siap membantu kebutuhan Anda
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ShoppingCart className="w-8 h-8 text-orange-600" />
                </div>
                <CardTitle>Harga Terjangkau</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Tarif rental yang kompetitif dengan berbagai pilihan durasi
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Cara Kerja
            </h2>
            <p className="text-xl text-gray-600">
              Proses rental yang mudah dalam 3 langkah
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold mb-2">Pilih Buku</h3>
              <p className="text-gray-600">
                Jelajahi katalog dan pilih buku programming yang Anda inginkan
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold mb-2">Buat Pesanan</h3>
              <p className="text-gray-600">
                Isi formulir pemesanan dengan data diri dan durasi rental
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold mb-2">Mulai Belajar</h3>
              <p className="text-gray-600">
                Setelah disetujui admin, Anda bisa langsung mulai belajar
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Siap Tingkatkan Skill Programming Anda?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Mulai perjalanan belajar Anda dengan koleksi buku terbaik
          </p>
          <Button 
            onClick={() => setShowCatalog(true)}
            className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-3 h-auto"
          >
            Mulai Sekarang
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-4">
            <BookOpen className="w-8 h-8 mr-2" />
            <span className="text-2xl font-bold">Rental Buku Programming</span>
          </div>
          <p className="text-gray-400">
            Platform terpercaya untuk menyewa buku programming berkualitas tinggi
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
